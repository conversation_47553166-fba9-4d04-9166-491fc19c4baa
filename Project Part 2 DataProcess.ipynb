%config Completer.use_jedi=False # comment if not needed
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler

%config Completer.use_jedi=False # comment if not needed

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

from sklearn.model_selection import GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.svm import SVC

from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report, roc_auc_score, roc_curve

print("Libraries imported successfully.")

# 1. Load the dataset  climate.csv

try:
    dataset = pd.read_csv('climate.csv') # Using the provided CSV file name

    print("Dataset preview:\n", dataset.head())
    # Display basic information about the data
    print("Dataset shape:", dataset.shape) # Output: Dataset shape: (rows, columns)
    print("First 5 rows of the dataset:") # Output: First 5 rows of the dataset:
    print(dataset.head())
    print("Dataset column names:", list(dataset.columns)) # Output: Dataset column names: [...]
except FileNotFoundError:
    # Error: 'classification_dataset.csv' not found. Please ensure the file is in the correct directory.
    print("Error: 'classification_dataset.csv' not found. Please ensure the file is in the correct directory.") 
