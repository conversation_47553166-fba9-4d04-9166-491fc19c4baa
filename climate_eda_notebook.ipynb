{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Climate Data Analysis Project\n", "\n", "## Section 1: Exploratory Data Analysis (EDA)\n", "\n", "This notebook contains comprehensive exploratory data analysis for the climate simulation dataset.\n", "\n", "**Author:** Data Science Team  \n", "**Date:** 2025-06-27  \n", "**Dataset:** climate.csv  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.1 Setup and Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "# Configure display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', None)\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the climate dataset\n", "df = pd.read_csv('climate.csv')\n", "\n", "print(f\"Dataset loaded successfully!\")\n", "print(f\"Shape: {df.shape}\")\n", "print(f\"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Dataset Overview"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display basic information about the dataset\n", "print(\"Dataset Information:\")\n", "print(\"=\" * 50)\n", "df.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display first few rows\n", "print(\"First 5 rows of the dataset:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for missing values\n", "missing_data = df.isnull().sum()\n", "missing_percentage = (missing_data / len(df)) * 100\n", "\n", "missing_summary = pd.DataFrame({\n", "    'Missing Count': missing_data,\n", "    'Missing Percentage': missing_percentage\n", "}).sort_values('Missing Count', ascending=False)\n", "\n", "print(\"Missing Values Summary:\")\n", "print(missing_summary[missing_summary['Missing Count'] > 0])\n", "\n", "if missing_summary['Missing Count'].sum() == 0:\n", "    print(\"✓ No missing values found in the dataset!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Statistical Description"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive statistical description\n", "print(\"Comprehensive Statistical Summary:\")\n", "print(\"=\" * 50)\n", "\n", "# Basic statistics\n", "desc_stats = df.describe()\n", "display(desc_stats)\n", "\n", "# Additional statistics\n", "additional_stats = pd.DataFrame({\n", "    'skewness': df.select_dtypes(include=[np.number]).skew(),\n", "    'kurtosis': df.select_dtypes(include=[np.number]).kurtosis(),\n", "    'variance': df.select_dtypes(include=[np.number]).var()\n", "})\n", "\n", "print(\"\\nAdditional Statistical Measures:\")\n", "display(additional_stats)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Target variable analysis\n", "target_col = 'outcome'\n", "feature_cols = [col for col in df.columns if col != target_col]\n", "\n", "print(f\"Target Variable Analysis: {target_col}\")\n", "print(\"=\" * 50)\n", "\n", "# Value counts and proportions\n", "target_counts = df[target_col].value_counts().sort_index()\n", "target_props = df[target_col].value_counts(normalize=True).sort_index()\n", "\n", "target_summary = pd.DataFrame({\n", "    'Count': target_counts,\n", "    'Proportion': target_props,\n", "    'Percentage': target_props * 100\n", "})\n", "\n", "display(target_summary)\n", "\n", "print(f\"\\nFeature Variables: {len(feature_cols)} features\")\n", "print(f\"Features: {feature_cols}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.4 Data Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Target variable distribution\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 5))\n", "\n", "# Bar plot\n", "target_counts.plot(kind='bar', ax=axes[0], color='skyblue', alpha=0.8)\n", "axes[0].set_title('Target Variable Distribution (Count)', fontsize=14, fontweight='bold')\n", "axes[0].set_xlabel('Outcome')\n", "axes[0].set_ylabel('Count')\n", "axes[0].tick_params(axis='x', rotation=0)\n", "\n", "# Pie chart\n", "axes[1].pie(target_counts.values, labels=target_counts.index, autopct='%1.1f%%', \n", "           colors=['lightcoral', 'lightblue'], startangle=90)\n", "axes[1].set_title('Target Variable Distribution (Proportion)', fontsize=14, fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feature distributions - Histograms\n", "n_features = len(feature_cols)\n", "n_cols = 4\n", "n_rows = (n_features + n_cols - 1) // n_cols\n", "\n", "fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))\n", "axes = axes.flatten() if n_rows > 1 else [axes] if n_rows == 1 else axes\n", "\n", "for i, col in enumerate(feature_cols):\n", "    if i < len(axes):\n", "        axes[i].hist(df[col], bins=30, alpha=0.7, color='steelblue', edgecolor='black')\n", "        axes[i].set_title(f'Distribution of {col}', fontsize=12, fontweight='bold')\n", "        axes[i].set_xlabel(col)\n", "        axes[i].set_ylabel('Frequency')\n", "        axes[i].grid(True, alpha=0.3)\n", "\n", "# Hide empty subplots\n", "for i in range(len(feature_cols), len(axes)):\n", "    axes[i].set_visible(False)\n", "\n", "plt.suptitle('Feature Distributions', fontsize=16, fontweight='bold', y=1.02)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Box plots for outlier detection\n", "fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))\n", "axes = axes.flatten() if n_rows > 1 else [axes] if n_rows == 1 else axes\n", "\n", "for i, col in enumerate(feature_cols):\n", "    if i < len(axes):\n", "        axes[i].boxplot(df[col], patch_artist=True, \n", "                       boxprops=dict(facecolor='lightblue', alpha=0.7))\n", "        axes[i].set_title(f'Box Plot of {col}', fontsize=12, fontweight='bold')\n", "        axes[i].set_ylabel(col)\n", "        axes[i].grid(True, alpha=0.3)\n", "\n", "# Hide empty subplots\n", "for i in range(len(feature_cols), len(axes)):\n", "    axes[i].set_visible(False)\n", "\n", "plt.suptitle('Feature Box Plots (Outlier Detection)', fontsize=16, fontweight='bold', y=1.02)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.5 Correlation Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Correlation matrix\n", "correlation_matrix = df[feature_cols].corr()\n", "\n", "# Plot correlation heatmap\n", "plt.figure(figsize=(16, 14))\n", "mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))\n", "sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,\n", "            square=True, linewidths=0.5, cbar_kws={\"shrink\": .8}, fmt='.2f')\n", "plt.title('Feature Correlation Matrix', fontsize=16, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Find highly correlated pairs\n", "high_corr_pairs = []\n", "for i in range(len(correlation_matrix.columns)):\n", "    for j in range(i+1, len(correlation_matrix.columns)):\n", "        corr_val = correlation_matrix.iloc[i, j]\n", "        if abs(corr_val) > 0.7:  # Threshold for high correlation\n", "            high_corr_pairs.append({\n", "                'Feature 1': correlation_matrix.columns[i],\n", "                'Feature 2': correlation_matrix.columns[j],\n", "                'Correlation': corr_val\n", "            })\n", "\n", "if high_corr_pairs:\n", "    print(\"\\nHighly Correlated Feature Pairs (|correlation| > 0.7):\")\n", "    high_corr_df = pd.DataFrame(high_corr_pairs)\n", "    display(high_corr_df.sort_values('Correlation', key=abs, ascending=False))\n", "else:\n", "    print(\"\\nNo highly correlated feature pairs found (|correlation| > 0.7)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.6 Feature-Target Relationship Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feature importance based on correlation with target\n", "feature_target_corr = df[feature_cols].corrwith(df[target_col]).abs().sort_values(ascending=False)\n", "\n", "plt.figure(figsize=(12, 8))\n", "feature_target_corr.plot(kind='barh', color='steelblue', alpha=0.8)\n", "plt.title('Feature Correlation with Target Variable (Absolute Values)', fontsize=14, fontweight='bold')\n", "plt.xlabel('Absolute Correlation')\n", "plt.ylabel('Features')\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Top 10 Features by Correlation with Target:\")\n", "display(feature_target_corr.head(10).to_frame('Absolute Correlation'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Distribution of top features by target class\n", "top_features = feature_target_corr.head(6).index.tolist()\n", "\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "axes = axes.flatten()\n", "\n", "for i, feature in enumerate(top_features):\n", "    for outcome in df[target_col].unique():\n", "        subset = df[df[target_col] == outcome][feature]\n", "        axes[i].hist(subset, alpha=0.6, label=f'Outcome {outcome}', bins=20)\n", "    \n", "    axes[i].set_title(f'Distribution of {feature} by Target', fontsize=12, fontweight='bold')\n", "    axes[i].set_xlabel(feature)\n", "    axes[i].set_ylabel('Frequency')\n", "    axes[i].legend()\n", "    axes[i].grid(True, alpha=0.3)\n", "\n", "plt.suptitle('Top Features Distribution by Target Class', fontsize=16, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.7 <PERSON><PERSON><PERSON> and Key Insights"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comprehensive summary\n", "print(\"EXPLORATORY DATA ANALYSIS SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\n📊 Dataset Overview:\")\n", "print(f\"   • Total samples: {len(df):,}\")\n", "print(f\"   • Total features: {len(feature_cols)}\")\n", "print(f\"   • Target variable: {target_col}\")\n", "print(f\"   • Missing values: {df.isnull().sum().sum()}\")\n", "print(f\"   • Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "\n", "print(f\"\\n🎯 Target Variable Distribution:\")\n", "for outcome, count in target_counts.items():\n", "    percentage = (count / len(df)) * 100\n", "    print(f\"   • Outcome {outcome}: {count:,} samples ({percentage:.1f}%)\")\n", "\n", "print(f\"\\n🔍 Feature Characteristics:\")\n", "print(f\"   • All features are numerical\")\n", "print(f\"   • Feature value ranges: [0, 1] (normalized)\")\n", "print(f\"   • Most correlated with target: {feature_target_corr.index[0]} ({feature_target_corr.iloc[0]:.3f})\")\n", "print(f\"   • Least correlated with target: {feature_target_corr.index[-1]} ({feature_target_corr.iloc[-1]:.3f})\")\n", "\n", "if high_corr_pairs:\n", "    print(f\"\\n⚠️  Multicollinearity Concerns:\")\n", "    print(f\"   • Found {len(high_corr_pairs)} highly correlated feature pairs\")\n", "    print(f\"   • Consider feature selection or dimensionality reduction\")\n", "else:\n", "    print(f\"\\n✅ Multicollinearity:\")\n", "    print(f\"   • No severe multicollinearity detected\")\n", "\n", "print(f\"\\n📈 Data Quality:\")\n", "print(f\"   • No missing values detected\")\n", "print(f\"   • Data appears to be pre-normalized\")\n", "print(f\"   • Ready for machine learning algorithms\")\n", "\n", "print(f\"\\n🚀 Next Steps:\")\n", "print(f\"   • Run data preprocessing pipeline\")\n", "print(f\"   • Split data into train/validation/test sets\")\n", "print(f\"   • Consider feature selection based on correlation analysis\")\n", "print(f\"   • Apply anomaly detection if needed\")\n", "print(f\"   • Proceed with model development\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}